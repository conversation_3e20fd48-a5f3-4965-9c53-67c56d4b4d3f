import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Settings, 
  Cpu, 
  DollarSign, 
  Shield, 
  Activity,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Download,
  Upload,
  Eye
} from 'lucide-react';

import { ProviderConfigForm } from './ProviderConfigForm';
import { CostManagementSettings } from './CostManagementSettings';
import { ReliabilityPerformanceSettings } from './ReliabilityPerformanceSettings';
import { 
  EnhancedUserSettings,
  ProviderConfig,
  DEFAULT_PROVIDER_CONFIGS,
  DEFAULT_COST_GUARD_CONFIG,
  DEFAULT_TOKEN_MONITOR_CONFIG,
  DEFAULT_FAILOVER_CONFIG,
  DEFAULT_RETRY_CONFIG,
  DEFAULT_PERFORMANCE_CONFIG,
  validateEnhancedUserSettings
} from '@/types/settings';

interface EnhancedSettingsPageProps {
  initialSettings?: Partial<EnhancedUserSettings>;
  onSave: (settings: EnhancedUserSettings) => Promise<void>;
  onTest?: (config: ProviderConfig, apiKey: string) => Promise<boolean>;
  currentUsage?: {
    currentCost: number;
    tokensUsed: number;
    requestsToday: number;
    costToday: number;
  };
  providerHealth?: Record<string, { isHealthy: boolean; lastCheck: Date; responseTime: number }>;
}

export const EnhancedSettingsPage: React.FC<EnhancedSettingsPageProps> = ({
  initialSettings = {},
  onSave,
  onTest,
  currentUsage,
  providerHealth,
}) => {
  console.log('EnhancedSettingsPage rendered with:', { initialSettings, currentUsage, providerHealth });
  const [settings, setSettings] = useState<EnhancedUserSettings>({
    // Legacy defaults
    planner_model: 'gpt-4o',
    critic_model: 'gpt-4o',
    default_max_iterations: 10,
    default_score_threshold: 0.95,
    telemetry_enabled: true,
    auto_create_pr: false,
    
    // Enhanced configuration
    provider_configs: DEFAULT_PROVIDER_CONFIGS,
    cost_guard_config: DEFAULT_COST_GUARD_CONFIG,
    token_monitor_config: DEFAULT_TOKEN_MONITOR_CONFIG,
    failover_config: DEFAULT_FAILOVER_CONFIG,
    retry_config: DEFAULT_RETRY_CONFIG,
    performance_config: DEFAULT_PERFORMANCE_CONFIG,
    
    config_version: 2,
    validation_errors: [],
    
    ...initialSettings,
  });

  const [apiKeys, setApiKeys] = useState({
    openai: '',
    anthropic: '',
    vertex_ai_project_id: '',
  });

  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState('providers');

  // Validate settings on change
  useEffect(() => {
    try {
      validateEnhancedUserSettings(settings);
      setValidationErrors([]);
    } catch (error: any) {
      const errors = error.errors?.map((e: any) => e.message) || [error.message];
      setValidationErrors(errors);
    }
  }, [settings]);

  const handleSave = async () => {
    if (validationErrors.length > 0) {
      setSaveStatus('error');
      return;
    }

    setIsSaving(true);
    setSaveStatus('idle');

    try {
      const enhancedSettings: EnhancedUserSettings = {
        ...settings,
        api_keys: apiKeys,
        updated_at: new Date().toISOString(),
      };

      await onSave(enhancedSettings);
      setSaveStatus('success');
      
      // Auto-hide success message after 3 seconds
      setTimeout(() => setSaveStatus('idle'), 3000);
    } catch (error) {
      setSaveStatus('error');
      console.error('Failed to save settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleProviderConfigChange = (role: 'planner' | 'critic', config: ProviderConfig) => {
    setSettings(prev => ({
      ...prev,
      provider_configs: {
        ...prev.provider_configs,
        [role]: config,
      },
    }));
  };

  const handleFallbackProvidersChange = (providers: ProviderConfig[]) => {
    setSettings(prev => ({
      ...prev,
      provider_configs: {
        ...prev.provider_configs,
        fallback_providers: providers,
      },
    }));
  };

  const exportSettings = () => {
    const exportData = {
      settings,
      apiKeys: { ...apiKeys, openai: '***', anthropic: '***' }, // Mask sensitive data
      exportedAt: new Date().toISOString(),
      version: settings.config_version,
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `dual-agent-settings-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importData = JSON.parse(e.target?.result as string);
        if (importData.settings) {
          setSettings(importData.settings);
        }
        setSaveStatus('success');
      } catch (error) {
        setSaveStatus('error');
        console.error('Failed to import settings:', error);
      }
    };
    reader.readAsText(file);
  };

  const getTabIcon = (tab: string) => {
    switch (tab) {
      case 'providers': return <Cpu className="w-4 h-4" />;
      case 'cost': return <DollarSign className="w-4 h-4" />;
      case 'reliability': return <Shield className="w-4 h-4" />;
      case 'monitoring': return <Activity className="w-4 h-4" />;
      default: return <Settings className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Enhanced Settings</h1>
            <p className="text-slate-400">
              Configure your dual-agent system with enterprise-grade controls
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant="outline" className="text-slate-300">
              Config v{settings.config_version}
            </Badge>
            {validationErrors.length === 0 ? (
              <CheckCircle className="w-5 h-5 text-green-400" />
            ) : (
              <AlertTriangle className="w-5 h-5 text-red-400" />
            )}
          </div>
        </div>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <Alert className="border-red-500/20 bg-red-500/10">
            <AlertTriangle className="h-4 w-4 text-red-500" />
            <AlertDescription className="text-red-200">
              <div className="font-medium mb-2">Configuration Issues:</div>
              <ul className="list-disc list-inside space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Save Status */}
        {saveStatus === 'success' && (
          <Alert className="border-green-500/20 bg-green-500/10">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <AlertDescription className="text-green-200">
              Settings saved successfully!
            </AlertDescription>
          </Alert>
        )}

        {saveStatus === 'error' && (
          <Alert className="border-red-500/20 bg-red-500/10">
            <AlertTriangle className="h-4 w-4 text-red-500" />
            <AlertDescription className="text-red-200">
              Failed to save settings. Please check your configuration and try again.
            </AlertDescription>
          </Alert>
        )}

        {/* Main Settings Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 bg-slate-800/50 border border-slate-700">
            <TabsTrigger value="providers" className="flex items-center gap-2">
              {getTabIcon('providers')}
              Providers
            </TabsTrigger>
            <TabsTrigger value="cost" className="flex items-center gap-2">
              {getTabIcon('cost')}
              Cost & Tokens
            </TabsTrigger>
            <TabsTrigger value="reliability" className="flex items-center gap-2">
              {getTabIcon('reliability')}
              Reliability
            </TabsTrigger>
            <TabsTrigger value="monitoring" className="flex items-center gap-2">
              {getTabIcon('monitoring')}
              Monitoring
            </TabsTrigger>
          </TabsList>

          {/* Provider Configuration */}
          <TabsContent value="providers" className="space-y-6">
            <div className="grid gap-6">
              <ProviderConfigForm
                label="Planner Agent"
                config={settings.provider_configs.planner}
                onChange={(config) => handleProviderConfigChange('planner', config)}
                apiKey={apiKeys.openai}
                onApiKeyChange={(key) => setApiKeys(prev => ({ ...prev, openai: key }))}
                testConnection={!!onTest}
                showAdvanced={true}
              />
              
              <ProviderConfigForm
                label="Critic Agent"
                config={settings.provider_configs.critic}
                onChange={(config) => handleProviderConfigChange('critic', config)}
                apiKey={apiKeys.anthropic}
                onApiKeyChange={(key) => setApiKeys(prev => ({ ...prev, anthropic: key }))}
                testConnection={!!onTest}
                showAdvanced={true}
              />
            </div>
          </TabsContent>

          {/* Cost Management */}
          <TabsContent value="cost">
            <CostManagementSettings
              costGuardConfig={settings.cost_guard_config}
              tokenMonitorConfig={settings.token_monitor_config}
              onCostGuardChange={(config) => setSettings(prev => ({ ...prev, cost_guard_config: config }))}
              onTokenMonitorChange={(config) => setSettings(prev => ({ ...prev, token_monitor_config: config }))}
              currentUsage={currentUsage}
            />
          </TabsContent>

          {/* Reliability & Performance */}
          <TabsContent value="reliability">
            <ReliabilityPerformanceSettings
              failoverConfig={settings.failover_config}
              retryConfig={settings.retry_config}
              performanceConfig={settings.performance_config}
              fallbackProviders={settings.provider_configs.fallback_providers}
              onFailoverChange={(config) => setSettings(prev => ({ ...prev, failover_config: config }))}
              onRetryChange={(config) => setSettings(prev => ({ ...prev, retry_config: config }))}
              onPerformanceChange={(config) => setSettings(prev => ({ ...prev, performance_config: config }))}
              onFallbackProvidersChange={handleFallbackProvidersChange}
              providerHealth={providerHealth}
            />
          </TabsContent>

          {/* Monitoring Dashboard */}
          <TabsContent value="monitoring">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Activity className="w-5 h-5 text-blue-400" />
                  Real-time Monitoring Dashboard
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <Eye className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                  <p className="text-slate-400">
                    Real-time monitoring dashboard will be implemented here
                  </p>
                  <p className="text-slate-500 text-sm mt-2">
                    Track provider health, costs, and performance metrics
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-6 border-t border-slate-700">
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={exportSettings}
              className="border-slate-600 text-slate-300 hover:text-white"
            >
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            
            <div className="relative">
              <input
                type="file"
                accept=".json"
                onChange={importSettings}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
              <Button
                variant="outline"
                className="border-slate-600 text-slate-300 hover:text-white"
              >
                <Upload className="w-4 h-4 mr-2" />
                Import
              </Button>
            </div>
          </div>

          <Button
            onClick={handleSave}
            disabled={isSaving || validationErrors.length > 0}
            className="bg-indigo-600 hover:bg-indigo-700 text-white"
          >
            {isSaving ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            {isSaving ? 'Saving...' : 'Save Settings'}
          </Button>
        </div>
      </div>
    </div>
  );
};
