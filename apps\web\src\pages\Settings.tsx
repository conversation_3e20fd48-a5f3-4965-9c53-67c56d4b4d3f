
import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Loader2, Settings as SettingsIcon } from "lucide-react";
import { toast } from "sonner";
import { supabase, isSupabaseConfigured } from "@/lib/supabase";
import { telemetry } from "@/lib/telemetry";
import { EnhancedSettingsPage } from "@/components/settings/EnhancedSettingsPage";
import {
  EnhancedUserSettings,
  DatabaseSettings,
  migrateFromLegacySettings,
  validateEnhancedUserSettings,
  ProviderConfig
} from "@/types/settings";

// Legacy interface for backward compatibility
interface LegacyUserSettings {
  id?: string;
  user_id?: string;
  planner_model: string;
  critic_model: string;
  default_max_iterations: number;
  default_score_threshold: number;
  telemetry_enabled: boolean;
  auto_create_pr: boolean;
  github_repo_owner?: string;
  github_repo_name?: string;
  openai_api_key_encrypted?: string;
  anthropic_api_key_encrypted?: string;
}

const Settings = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState<EnhancedUserSettings | null>(null);

  // Debug logging
  console.log('Settings component mounted');
  const [currentUsage, setCurrentUsage] = useState({
    currentCost: 0,
    tokensUsed: 0,
    requestsToday: 0,
    costToday: 0,
  });
  const [providerHealth, setProviderHealth] = useState<Record<string, {
    isHealthy: boolean;
    lastCheck: Date;
    responseTime: number
  }>>({});

  useEffect(() => {
    console.log('Settings useEffect running');
    try {
      // Check if Supabase is configured
      if (!isSupabaseConfigured()) {
        console.warn('Supabase not configured, using mock data');
        setLoading(false);
        // Set default settings for development
        const defaultSettings: Partial<EnhancedUserSettings> = migrateFromLegacySettings({
          planner_model: "gpt-4o",
          critic_model: "gpt-4o",
          default_max_iterations: 10,
          default_score_threshold: 0.95,
          telemetry_enabled: true,
          auto_create_pr: false,
        });
        console.log('Setting default settings:', defaultSettings);
        setSettings(defaultSettings as EnhancedUserSettings);
        return;
      }

      console.log('Supabase configured, loading settings');
      loadSettings();
      loadCurrentUsage();
      loadProviderHealth();
      try {
        telemetry.trackPageView('settings');
      } catch (error) {
        console.warn('Telemetry error:', error);
      }
    } catch (error) {
      console.error('Settings useEffect error:', error);
      setLoading(false);
    }
  }, []);

  const loadSettings = async () => {
    try {
      if (!isSupabaseConfigured()) {
        // Use default settings when Supabase is not configured
        const defaultSettings: Partial<EnhancedUserSettings> = migrateFromLegacySettings({
          planner_model: "gpt-4o",
          critic_model: "gpt-4o",
          default_max_iterations: 10,
          default_score_threshold: 0.95,
          telemetry_enabled: true,
          auto_create_pr: false,
        });
        setSettings(defaultSettings as EnhancedUserSettings);
        return;
      }

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast.error("Please sign in to access settings");
        return;
      }

      const { data, error } = await supabase
        .from('settings')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') { // Not found error
        console.error('Error loading settings:', error);
        toast.error("Failed to load settings");
        return;
      }

      if (data) {
        // Check if we have enhanced settings or need to migrate
        if (data.provider_configs) {
          // Already enhanced settings
          setSettings(data as EnhancedUserSettings);
        } else {
          // Legacy settings - migrate to new format
          const migratedSettings = migrateFromLegacySettings(data as LegacyUserSettings);
          setSettings(migratedSettings as EnhancedUserSettings);
        }
      } else {
        // No settings found - create default enhanced settings
        const defaultSettings: Partial<EnhancedUserSettings> = migrateFromLegacySettings({
          planner_model: "gpt-4o",
          critic_model: "gpt-4o",
          default_max_iterations: 10,
          default_score_threshold: 0.95,
          telemetry_enabled: true,
          auto_create_pr: false,
        });
        setSettings(defaultSettings as EnhancedUserSettings);
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      toast.error("Failed to load settings");
    } finally {
      setLoading(false);
    }
  };

  const loadCurrentUsage = async () => {
    // TODO: Implement real usage tracking
    // For now, simulate some usage data
    setCurrentUsage({
      currentCost: 0.45,
      tokensUsed: 12500,
      requestsToday: 8,
      costToday: 1.23,
    });
  };

  const loadProviderHealth = async () => {
    // TODO: Implement real provider health monitoring
    // For now, simulate provider health data
    setProviderHealth({
      'openai': { isHealthy: true, lastCheck: new Date(), responseTime: 245 },
      'vertex-ai': { isHealthy: true, lastCheck: new Date(), responseTime: 312 },
      'anthropic': { isHealthy: false, lastCheck: new Date(), responseTime: 1200 },
    });
  };

  const saveSettings = async (enhancedSettings: EnhancedUserSettings) => {
    try {
      if (!isSupabaseConfigured()) {
        // Just update local state when Supabase is not configured
        setSettings(enhancedSettings);
        toast.success("Settings saved locally (Supabase not configured)");
        return;
      }

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast.error("Please sign in to save settings");
        return;
      }

      // Validate settings before saving
      try {
        validateEnhancedUserSettings(enhancedSettings);
      } catch (validationError) {
        console.error('Settings validation failed:', validationError);
        toast.error("Settings validation failed. Please check your configuration.");
        throw validationError;
      }

      // Prepare the settings data for database
      const settingsData: Partial<DatabaseSettings> = {
        user_id: user.id,

        // Legacy fields for backward compatibility
        planner_model: enhancedSettings.planner_model,
        critic_model: enhancedSettings.critic_model,
        default_max_iterations: enhancedSettings.default_max_iterations,
        default_score_threshold: enhancedSettings.default_score_threshold,
        telemetry_enabled: enhancedSettings.telemetry_enabled,
        auto_create_pr: enhancedSettings.auto_create_pr,
        github_repo_owner: enhancedSettings.github_repo_owner || null,
        github_repo_name: enhancedSettings.github_repo_name || null,

        // Enhanced configuration (JSONB fields)
        provider_configs: enhancedSettings.provider_configs,
        cost_guard_config: enhancedSettings.cost_guard_config,
        token_monitor_config: enhancedSettings.token_monitor_config,
        failover_config: enhancedSettings.failover_config,
        retry_config: enhancedSettings.retry_config,
        performance_config: enhancedSettings.performance_config,

        // API keys (encrypted)
        openai_api_key_encrypted: enhancedSettings.api_keys?.openai || null,
        anthropic_api_key_encrypted: enhancedSettings.api_keys?.anthropic || null,

        // Vertex AI configuration
        vertex_ai_project_id: enhancedSettings.vertex_ai_project_id || null,
        vertex_ai_location: enhancedSettings.vertex_ai_location || 'us-central1',

        // Metadata
        config_version: enhancedSettings.config_version,
        updated_at: new Date().toISOString(),
      };

      const { error } = await supabase
        .from('settings')
        .upsert(settingsData, { onConflict: 'user_id' });

      if (error) {
        console.error('Error saving settings:', error);
        toast.error("Failed to save settings");
        throw error;
      }

      // Update local state
      setSettings(enhancedSettings);

      toast.success("Settings saved successfully!");
      telemetry.trackEvent('enhanced_settings_saved', {
        config_version: enhancedSettings.config_version,
        planner_provider: enhancedSettings.provider_configs.planner.type,
        critic_provider: enhancedSettings.provider_configs.critic.type,
        cost_guard_enabled: enhancedSettings.cost_guard_config.enabled,
        failover_enabled: enhancedSettings.failover_config.enabled,
        streaming_enabled: enhancedSettings.performance_config.streamingEnabled,
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error("Failed to save settings");
      throw error;
    }
  };

  const testProviderConnection = async (config: ProviderConfig, apiKey: string): Promise<boolean> => {
    // TODO: Implement actual provider connection testing
    // For now, simulate a test
    await new Promise(resolve => setTimeout(resolve, 1000));
    return Math.random() > 0.3; // 70% success rate for demo
  };

  if (loading) {
    console.log('Settings loading state');
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="w-6 h-6 animate-spin text-indigo-400" />
          <span className="text-white">Loading enhanced settings...</span>
        </div>
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <SettingsIcon className="w-12 h-12 text-slate-400 mx-auto mb-4" />
          <p className="text-white">Failed to load settings</p>
          <Button
            onClick={loadSettings}
            className="mt-4 bg-indigo-600 hover:bg-indigo-700"
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <div className="border-b border-slate-800 bg-slate-900/95 backdrop-blur-sm">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/dashboard')}
              className="text-slate-400 hover:text-white"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Dashboard
            </Button>
            <h1 className="text-2xl font-bold text-white">Enhanced Settings</h1>
            <Badge variant="secondary" className="bg-indigo-600/20 text-indigo-300 border-indigo-500/30">
              v{settings.config_version}
            </Badge>
          </div>
        </div>
      </div>

      {/* Enhanced Settings Content */}
      <div className="px-6 py-8">
        <EnhancedSettingsPage
          initialSettings={settings}
          onSave={saveSettings}
          onTest={testProviderConnection}
          currentUsage={currentUsage}
          providerHealth={providerHealth}
        />
      </div>



    </div>
  );
};

export default Settings;
